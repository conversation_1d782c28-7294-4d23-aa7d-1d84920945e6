@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 271 81% 28%; /* Dark purple */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 271 81% 28%; /* Dark purple */
    --radius: 0.5rem;
    --checkbox-checked: 271 81% 28%; /* Dark purple */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 271 81% 28%; /* Dark purple */
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 271 81% 28%; /* Dark purple */
    --checkbox-checked: 271 81% 28%; /* Dark purple */
  }
}

@layer components {
  /* Remove vertical tree lines from sidebar sub-menus */
  [data-sidebar="menu-sub"] {
    @apply border-l-0 px-0;
  }

  /* Ensure chevrons rotate correctly */
  [data-state="open"] > [data-sidebar="menu-button"] > svg:last-child {
    @apply rotate-90;
  }

  /* Custom styling for sidebar menu buttons */
  [data-sidebar="menu-button"][data-active="true"] {
    background-color: #e3e3e3 !important;
  }

  [data-sidebar="menu-button"]:hover:not([data-active="true"]) {
    background-color: #ececec !important;
  }

  /* Compact list tabs styling */
  .compact-list-tabs [data-sidebar="menu-button"] {
    height: 26px;
  }

  /* Enhanced accordion animations with better timing */
  @keyframes accordion-down-enhanced {
    from {
      height: 0;
      opacity: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
      opacity: 1;
    }
  }

  @keyframes accordion-up-enhanced {
    from {
      height: var(--radix-accordion-content-height);
      opacity: 1;
    }
    to {
      height: 0;
      opacity: 0;
    }
  }

  /* Enhanced sequential fade animations for list items */
  @keyframes list-item-fade-in-sequential {
    from {
      opacity: 0;
      transform: translateY(-8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes list-item-fade-out-sequential {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(-8px);
    }
  }

  .animate-accordion-down {
    animation: accordion-down-enhanced 0.35s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-accordion-up {
    animation: accordion-up-enhanced 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Sequential fade-in classes with staggered delays */
  .list-item-fade-in {
    opacity: 0;
    animation: list-item-fade-in-sequential 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .list-item-fade-out {
    opacity: 1;
    animation: list-item-fade-out-sequential 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  /* Staggered delays for top-to-bottom fade-in */
  .list-item-fade-in:nth-child(1) {
    animation-delay: 0ms;
  }
  .list-item-fade-in:nth-child(2) {
    animation-delay: 60ms;
  }
  .list-item-fade-in:nth-child(3) {
    animation-delay: 120ms;
  }
  .list-item-fade-in:nth-child(4) {
    animation-delay: 180ms;
  }
  .list-item-fade-in:nth-child(5) {
    animation-delay: 240ms;
  }
  .list-item-fade-in:nth-child(6) {
    animation-delay: 300ms;
  }
  .list-item-fade-in:nth-child(7) {
    animation-delay: 360ms;
  }
  .list-item-fade-in:nth-child(8) {
    animation-delay: 420ms;
  }

  /* Reverse staggered delays for bottom-to-top fade-out */
  .list-item-fade-out:nth-child(1) {
    animation-delay: 350ms;
  }
  .list-item-fade-out:nth-child(2) {
    animation-delay: 280ms;
  }
  .list-item-fade-out:nth-child(3) {
    animation-delay: 210ms;
  }
  .list-item-fade-out:nth-child(4) {
    animation-delay: 140ms;
  }
  .list-item-fade-out:nth-child(5) {
    animation-delay: 70ms;
  }
  .list-item-fade-out:nth-child(6) {
    animation-delay: 0ms;
  }
  .list-item-fade-out:nth-child(7) {
    animation-delay: 0ms;
  }
  .list-item-fade-out:nth-child(8) {
    animation-delay: 0ms;
  }

  /* Prevent layout shifts during enhanced animations */
  .compact-list-tabs .animate-accordion-down,
  .compact-list-tabs .animate-accordion-up {
    will-change: height, opacity, transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Enhanced chevron rotation with smoother timing */
  .chevron-enhanced {
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Smooth hover effects for better visual feedback */
  [data-sidebar="menu-button"]:hover {
    transition: background-color 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
  }

  /* Enhanced collapsible content styling */
  .collapsible-content-enhanced {
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .collapsible-content-enhanced[data-state="closed"] {
    animation: accordion-up-enhanced 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .collapsible-content-enhanced[data-state="open"] {
    animation: accordion-down-enhanced 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Refined drawer animation keyframes - pure translation without opacity */
  @keyframes drawer-in {
    from {
      transform: translateY(-20px);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes drawer-out {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(-20px);
    }
  }

  .animate-drawer-in {
    animation: drawer-in 500ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-drawer-out {
    animation: drawer-out 500ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  /* Ensure sidebar menu items have no gaps */
  [data-sidebar="menu"] {
    gap: 0 !important;
  }

  /* Ensure menu buttons extend fully */
  [data-sidebar="menu-button"] {
    width: 100%;
  }

  /* Add rounded corners for active and hover states */
  [data-sidebar="menu-button"][data-active="true"],
  [data-sidebar="menu-button"]:hover {
    border-radius: 0.375rem; /* This is the equivalent of rounded-md in Tailwind */
  }

  /* Remove any padding that might cause gaps */
  [data-sidebar="menu-item"] {
    padding: 0;
    width: 100%;
  }

  /* Task container and selection styling */
  .task-container {
    width: 100%;
    padding: 0 16px;
    box-sizing: border-box;
    position: relative;
  }

  .task-item-wrapper {
    width: 100%;
    padding: 0 8px;
    box-sizing: border-box;
  }

  /* Add transition to task items for smoother state changes */
  .task-item {
    position: relative;
    transition: background-color 0.12s ease-out, opacity 0.2s ease, transform 0.2s ease !important;
  }

  /* Ensure hover states have quick transitions */
  .task-item:hover {
    transition: background-color 0.08s ease-out !important; /* Even faster for hover */
  }

  /* Disable hover effects during drag operations to prevent sticky states */
  .dragging-active .task-item:hover,
  .dragging-active .task-container:hover {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* Ensure hover effects are properly reset after drag operations */
  .hover-reset .task-item,
  .hover-reset .task-container {
    pointer-events: none !important;
  }

  .hover-reset .task-item *,
  .hover-reset .task-container * {
    pointer-events: none !important;
  }

  /* Task selection styling - contained within task boundaries */
  .task-selected {
    background-color: #f3f4f4 !important; /* Light grey background */
    border-radius: 6px;
    transition: background-color 0.12s ease-out !important; /* Faster, smoother transition */
  }

  /* Task dragging styling - improved visual representation */
  .task-dragging {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    border: 2px solid #3b82f6 !important; /* Thicker blue border */
    border-radius: 6px !important;
    /* Use box-shadow instead of ring to avoid size increase */
    transform: scale(1) !important; /* Prevent size increase */
  }

  /* Ensure the selection doesn't extend to the edges */
  [data-selected="true"] {
    background-color: transparent;
  }

  /* Checkbox styling - comprehensive fix for visual consistency */
  /* Target the Radix UI checkbox root element specifically */
  button[role="checkbox"] {
    border-radius: 2px !important; /* Force consistent rounded corners */
    width: 14px !important; /* Match task item size h-3.5 */
    height: 14px !important; /* Match task item size w-3.5 */
    min-width: 14px !important; /* Prevent shrinking */
    min-height: 14px !important; /* Prevent shrinking */
    box-sizing: border-box !important; /* Ensure proper sizing */
  }

  /* Checked state styling */
  button[role="checkbox"][data-state="checked"] {
    background-color: #4a1d96 !important; /* Dark purple */
    border-color: #4a1d96 !important;
    border-radius: 2px !important; /* Preserve rounded corners */
  }

  /* Ensure the checkbox indicator (checkmark) is properly sized */
  button[role="checkbox"] svg {
    width: 10px !important; /* Slightly smaller than container */
    height: 10px !important; /* Slightly smaller than container */
  }

  /* Drag and drop styles */
  [data-dragging="true"] {
    opacity: 0.6;
    cursor: grabbing !important;
    z-index: 999;
    will-change: transform;
    transition: none !important; /* Disable transitions during drag */
  }

  /* Add hardware acceleration for drag operations */
  .transform-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Drag overlay wrapper and container - exact match to task container structure */
  .drag-overlay-wrapper {
    width: 100%;
    padding: 0 16px;
    box-sizing: border-box;
    pointer-events: none;
  }

  .drag-overlay-container {
    width: 100%;
    padding: 0 8px;
    box-sizing: border-box;
    position: relative;
  }

  /* Dragged task item specific styling - exact match to selected task width */
  .dragged-task-item {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 2px solid #3b82f6 !important; /* Thicker blue border */
    border-radius: 6px !important;
    background-color: white !important;
    box-sizing: border-box !important;
  }

  /* Optimize drag overlay rendering */
  [data-dnd-drag-overlay] {
    will-change: transform;
    pointer-events: none;
    transition: none !important;
    transform: translateZ(0);
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    opacity: 1 !important; /* Ensure full opacity */
  }

  /* Ensure drag overlay doesn't expand */
  [data-dnd-drag-overlay] > div {
    width: 100% !important;
    box-sizing: border-box !important;
  }

  [data-multi-drag="true"] {
    opacity: 0.6;
    border: 1px dashed rgba(0, 0, 0, 0.2);
  }

  [data-droppable-id] {
    transition: background-color 0.2s ease, border-color 0.2s ease;
  }

  [data-droppable-id].drag-over {
    background-color: rgba(0, 0, 0, 0.05);
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: 0.375rem;
  }

  /* Six-dot drag handle positioning */
  .task-container .grip-handle {
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 10;
  }

  .task-container:hover .grip-handle {
    opacity: 1;
  }

  /* Keep the grip handle visible when task is expanded */
  .task-container[data-expanded="true"] .grip-handle {
    opacity: 1;
  }

  /* Ensure the grip handle stays with the title in expanded view */
  .expanded-task-item .grip-handle {
    top: 17px !important;
    transform: none !important;
  }

  /* Animation for task movement - shorter duration */
  @keyframes task-moved {
    0% {
      background-color: rgba(0, 0, 0, 0.05);
    }
    100% {
      background-color: transparent;
    }
  }

  .task-moved {
    animation: task-moved 0.5s ease-out; /* Reduced from 1s to 0.5s */
  }

  /* Bottom drop area styling */
  [data-bottom-drop-area] {
    min-height: 16px;
    transition: all 0.2s ease;
  }

  [data-bottom-drop-area][data-over="true"] {
    min-height: 40px;
    background-color: rgba(0, 0, 0, 0.05);
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: 0.375rem;
    margin: 8px 0;
  }

  /* Task list container */
  .task-list-container {
    width: 100%;
    box-sizing: border-box;
  }

  /* Expanded task item styling */
  .expanded-task-item {
    z-index: 10;
    position: relative;
  }

  /* Task title styling - maintain consistent font size */
  .expanded-title,
  .expanded-title-input {
    font-size: 14px !important; /* Match the inline task view font size */
    font-weight: normal !important;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
  }

  /* Task title focus and editing styles */
  .expanded-title-input {
    padding: 0 !important;
    margin: 0 !important;
    height: auto !important;
    caret-color: #3b82f6 !important; /* Blue cursor for better visibility */
    user-select: none !important; /* Prevent text selection */
  }

  .expanded-title {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: clip !important;
  }

  .expanded-checkbox {
    margin-top: 2px;
  }

  .expanded-task-actions {
    opacity: 0;
    pointer-events: none;
  }

  /* Notes field styling */
  .expanded-notes {
    margin-left: 24px !important; /* Align with the title text, accounting for checkbox */
  }

  /* Animation for expanded task view */
  @keyframes drawer-in {
    from {
      max-height: 0;
      opacity: 0;
    }
    to {
      max-height: 500px;
      opacity: 1;
    }
  }

  @keyframes drawer-out {
    from {
      max-height: 500px;
      opacity: 1;
    }
    to {
      max-height: 0;
      opacity: 0;
    }
  }

  .animate-drawer-in {
    animation: drawer-in 0.3s ease-out forwards;
  }

  .animate-drawer-out {
    animation: drawer-out 0.3s ease-in forwards;
  }

  /* Prevent notes field from getting auto-focus */
  .expanded-task-view textarea:focus {
    box-shadow: none !important;
  }

  /* Project view styling */
  .project-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }

  .project-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    padding: 0;
  }

  .project-notes-label {
    color: #9ca3af;
    font-size: 1rem;
    font-weight: normal;
    margin-bottom: 0.5rem;
  }

  .project-notes-content {
    color: #6b7280;
    font-size: 0.875rem;
    white-space: pre-wrap;
    min-height: 24px;
  }

  /* Remove box shadow from textarea when focused */
  .notes-textarea:focus {
    box-shadow: none !important;
    border-color: transparent !important;
  }

  /* Ensure the project view has proper spacing */
  .project-view-container {
    padding: 1.5rem;
  }
}

/* Calendar styling */
.rdp {
  --rdp-cell-size: 40px;
  --rdp-accent-color: #f3f4f6;
  --rdp-background-color: #f3f4f6;
  --rdp-accent-color-dark: #e5e7eb;
  --rdp-background-color-dark: #e5e7eb;
  --rdp-outline: 2px solid var(--rdp-accent-color);
  --rdp-outline-selected: 2px solid var(--rdp-accent-color);
  margin: 0;
}

.rdp-months {
  justify-content: center;
}

.rdp-month {
  background-color: #fff;
}

.rdp-caption {
  padding: 0;
  margin-bottom: 0.5rem;
}

.rdp-nav {
  position: absolute;
  top: 0.5rem;
  width: 100%;
}

.rdp-nav_button {
  width: 28px;
  height: 28px;
  padding: 0;
  background-color: transparent;
}

.rdp-nav_button:hover {
  background-color: var(--rdp-background-color);
}

.rdp-nav_button_previous {
  position: absolute;
  left: 0.5rem;
}

.rdp-nav_button_next {
  position: absolute;
  right: 0.5rem;
}

.rdp-caption_label {
  font-size: 1rem;
  font-weight: 500;
}

.rdp-head_cell {
  font-weight: 500;
  font-size: 0.875rem;
  color: #6b7280;
  padding-bottom: 0.5rem;
}

.rdp-cell {
  height: var(--rdp-cell-size);
  width: var(--rdp-cell-size);
}

.rdp-day {
  height: var(--rdp-cell-size);
  width: var(--rdp-cell-size);
  font-size: 0.875rem;
  border-radius: 0;
}

.rdp-day_selected,
.rdp-day_selected:focus-visible,
.rdp-day_selected:hover {
  background-color: var(--rdp-background-color);
  color: inherit;
}

.rdp-day:hover {
  background-color: var(--rdp-background-color);
}

.rdp-day_outside {
  opacity: 0.5;
}

.rdp-day_today {
  font-weight: 500;
}

/* Fix for modeless date picker positioning */
.modeless-date-picker {
  position: fixed;
  z-index: 1000;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-radius: var(--radius);
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Fixed-width calendar styles */
.fixed-calendar {
  width: 288px !important; /* 7 columns × 40px + padding */
}

.fixed-calendar .rdp-months {
  display: block !important;
  width: 100% !important;
}

.fixed-calendar .rdp-month {
  width: 100% !important;
}

.fixed-calendar .rdp-table {
  width: 100% !important;
}

.fixed-calendar .rdp-head_row,
.fixed-calendar .rdp-row {
  display: grid !important;
  grid-template-columns: repeat(7, 1fr) !important;
  width: 100% !important;
}

.fixed-calendar .rdp-head_cell,
.fixed-calendar .rdp-cell {
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.fixed-calendar .rdp-head_cell {
  height: 24px !important;
}

.fixed-calendar .rdp-day {
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
}

.fixed-calendar .rdp-nav {
  position: absolute !important;
  width: 100% !important;
}

.fixed-calendar .rdp-nav_button_previous {
  position: absolute !important;
  left: 1rem !important;
}

.fixed-calendar .rdp-nav_button_next {
  position: absolute !important;
  right: 1rem !important;
}

.fixed-calendar .rdp-caption {
  padding: 0 !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.fixed-calendar .rdp-caption_label {
  font-size: 1rem !important;
  font-weight: 500 !important;
}

/* Date picker container */
.date-picker-container {
  width: 336px !important;
}
